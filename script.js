// Language switching functionality
document.addEventListener("DOMContentLoaded", function () {
  const langButtons = document.querySelectorAll(".lang-btn");
  const html = document.documentElement;
  const body = document.body;

  // Translation content
  const translations = {
    ar: {
      title: 'معرض "صنع في ليبيا" - Made in Libya Exhibition',
      headerArabic: "جودة وطنية بأسواق واعدة",
      headerEnglish: "National quality in promising markets",
      mainTitle:
        'يُعد معرض "صنع في ليبيا" منصة وطنية حيوية تهدف إلى تسليط الضوء على قدرات وإمكانيات القطاع الصناعي والإنتاجي الليبي',
      description: [
        "تعزيز ثقافة المنتج المحلي لدى المستهلكين، وفتح آفاق جديدة للشركات والمصانع الليبية نحو النمو والتوسع.",
        "تنظيم هذا المعرض، في مدينة بنغازي، العاصمة الاقتصادية والتجارية الثانية لليبيا، ليؤكد على أهمية المدينة كمركز حيوي للنشاط الاقتصادي وناقذة للمنتجات الليبية نحو الأسواق المحلية والإقليمية.",
        "شركة أجيال للخدمات الإعلامية والمعارض والمؤتمرات، بخبرتها الواسعة، مسؤولة تنظم هذا الحدث الهام لضمان تقديمه بأعلى معايير الجودة والاحترافية",
      ],
      navigation: {
        home: "الرئيسية",
        vision: "رؤية المعرض",
        message: "الرسالة",
        objectives: "أهداف المعرض",
        companies: "الشركات الليبية",
        audience: "الجمهور المستهدف",
      },
    },
    en: {
      title: 'Made in Libya Exhibition - معرض "صنع في ليبيا"',
      headerArabic: "National quality in promising markets",
      headerEnglish: "جودة وطنية بأسواق واعدة",
      mainTitle:
        'The "Made in Libya" exhibition is a vital national platform aimed at highlighting the capabilities and potential of the Libyan industrial and productive sector',
      description: [
        "Promoting the culture of local products among consumers, and opening new horizons for Libyan companies and factories towards growth and expansion.",
        "Organizing this exhibition in the city of Benghazi, Libya's second economic and commercial capital, confirms the importance of the city as a vital center for economic activity and a gateway for Libyan products to local and regional markets.",
        "Ajyal Company for Media Services, Exhibitions and Conferences, with its extensive experience, is responsible for organizing this important event to ensure its delivery to the highest standards of quality and professionalism.",
      ],
      navigation: {
        home: "Home",
        vision: "Exhibition Vision",
        message: "Message",
        objectives: "Objectives",
        companies: "Libyan Companies",
        audience: "Target Audience",
      },
    },
  };

  // Function to switch language
  function switchLanguage(lang) {
    const isArabic = lang === "ar";

    // Update HTML attributes
    html.setAttribute("lang", lang);
    html.setAttribute("dir", isArabic ? "rtl" : "ltr");

    // Update document title
    document.title = translations[lang].title;

    // Update button states
    langButtons.forEach((btn) => {
      const btnLang = btn.getAttribute("data-lang");
      btn.classList.toggle("active", btnLang === lang);
    });

    // Update content
    const arabicTitle = document.querySelector(".arabic-title");
    const englishSubtitle = document.querySelector(".english-subtitle");
    const mainTitle = document.querySelector(".main-title");
    const descriptionText = document.querySelector(".description-text");

    if (arabicTitle) arabicTitle.textContent = translations[lang].headerArabic;
    if (englishSubtitle)
      englishSubtitle.textContent = translations[lang].headerEnglish;
    if (mainTitle) mainTitle.textContent = translations[lang].mainTitle;

    if (descriptionText) {
      descriptionText.innerHTML = translations[lang].description
        .map((text) => `<p>${text}</p>`)
        .join("");
    }

    // Update navigation
    const navLinks = document.querySelectorAll(".nav-link");
    navLinks.forEach((link) => {
      const href = link.getAttribute("href");
      const key = href.replace("#", "");
      if (translations[lang].navigation[key]) {
        link.textContent = translations[lang].navigation[key];
      }
    });

    // Update all elements with data attributes
    const elementsWithData = document.querySelectorAll("[data-ar][data-en]");
    elementsWithData.forEach((element) => {
      const text = element.getAttribute(`data-${lang}`);
      if (text) {
        element.textContent = text;
      }
    });

    // Store language preference
    localStorage.setItem("preferred-language", lang);
  }

  // Event listeners for language buttons
  langButtons.forEach((btn) => {
    btn.addEventListener("click", () => {
      const lang = btn.getAttribute("data-lang");
      switchLanguage(lang);
    });
  });

  // Smooth scroll navigation functionality
  function scrollToSection(sectionId) {
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
      targetSection.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }

    // Update navigation active state
    const navLinks = document.querySelectorAll(".nav-link");
    navLinks.forEach((link) => {
      link.classList.remove("active");
      if (link.getAttribute("href") === `#${sectionId}`) {
        link.classList.add("active");
      }
    });
  }

  // Add navigation event listeners
  const navLinks = document.querySelectorAll(".nav-link");
  navLinks.forEach((link) => {
    link.addEventListener("click", (e) => {
      e.preventDefault();
      const sectionId = link.getAttribute("href").replace("#", "");
      scrollToSection(sectionId);
    });
  });

  // Intersection Observer for updating active navigation
  const observerOptions = {
    root: null,
    rootMargin: "-50% 0px -50% 0px",
    threshold: 0,
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const sectionId = entry.target.id;
        const navLinks = document.querySelectorAll(".nav-link");
        navLinks.forEach((link) => {
          link.classList.remove("active");
          if (link.getAttribute("href") === `#${sectionId}`) {
            link.classList.add("active");
          }
        });
      }
    });
  }, observerOptions);

  // Observe all sections
  const sections = document.querySelectorAll(".section");
  sections.forEach((section) => {
    observer.observe(section);
  });

  // Initialize with saved language or default to Arabic
  const savedLang = localStorage.getItem("preferred-language") || "ar";
  switchLanguage(savedLang);

  // Smooth scroll animation for better UX
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    });
  });

  // Add fade-in animation on load
  body.style.opacity = "0";
  body.style.transition = "opacity 0.5s ease-in-out";

  setTimeout(() => {
    body.style.opacity = "1";
  }, 100);

  // Scroll Animation System
  function initScrollAnimations() {
    const animatedElements = document.querySelectorAll(
      ".animate-on-scroll, .animate-fade-in, .animate-slide-left, .animate-slide-right, .animate-scale, .animate-stagger, .animate-card, .animate-image, .animate-title, .animate-text"
    );

    const scrollObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Add a small delay to make the animation more noticeable
            setTimeout(() => {
              entry.target.classList.add("animate-in");
            }, 100);

            // Stop observing this element once it's animated
            scrollObserver.unobserve(entry.target);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      }
    );

    animatedElements.forEach((element) => {
      scrollObserver.observe(element);

      // Check if element is already in viewport and animate immediately
      const rect = element.getBoundingClientRect();
      const isInViewport = rect.top < window.innerHeight && rect.bottom > 0;

      if (isInViewport) {
        setTimeout(() => {
          element.classList.add("animate-in");
          scrollObserver.unobserve(element);
        }, 200);
      }
    });
  }

  // Immediately animate elements in the active section (home section)
  setTimeout(() => {
    const activeSection = document.querySelector(".section.active");
    if (activeSection) {
      const activeElements = activeSection.querySelectorAll(
        ".animate-on-scroll, .animate-fade-in, .animate-slide-left, .animate-slide-right, .animate-scale, .animate-stagger, .animate-card, .animate-image, .animate-title, .animate-text"
      );

      activeElements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add("animate-in");
        }, index * 200); // Stagger the animations
      });
    }
  }, 300);

  // Initialize scroll animations
  initScrollAnimations();

  // Safety fallback: ensure all text is visible after 3 seconds
  setTimeout(() => {
    const hiddenElements = document.querySelectorAll(
      ".animate-on-scroll:not(.animate-in), .animate-fade-in:not(.animate-in), .animate-slide-left:not(.animate-in), .animate-slide-right:not(.animate-in), .animate-scale:not(.animate-in), .animate-stagger:not(.animate-in), .animate-card:not(.animate-in), .animate-image:not(.animate-in), .animate-title:not(.animate-in), .animate-text:not(.animate-in)"
    );

    hiddenElements.forEach((element) => {
      element.classList.add("animate-in");
    });
  }, 3000);

  // Function to add staggered animations to child elements
  function addStaggeredAnimations(
    parentSelector,
    childSelector,
    animationClass = "animate-stagger"
  ) {
    const parents = document.querySelectorAll(parentSelector);

    parents.forEach((parent) => {
      const children = parent.querySelectorAll(childSelector);
      children.forEach((child, index) => {
        child.classList.add(animationClass);
        if (index < 6) {
          child.classList.add(`animate-delay-${index + 1}`);
        }
      });
    });
  }

  // Apply staggered animations to specific components
  setTimeout(() => {
    // Objectives grid items
    addStaggeredAnimations(
      ".objectives-grid",
      ".objective-item",
      "animate-card"
    );

    // Sectors grid items
    addStaggeredAnimations(".sectors-grid", ".sector-card", "animate-card");

    // Audience grid items
    addStaggeredAnimations(
      ".audience-grid",
      ".audience-item",
      "animate-slide-left"
    );

    // Visitors grid items
    addStaggeredAnimations(
      ".visitors-grid",
      ".visitor-category",
      "animate-slide-right"
    );

    // Navigation items
    addStaggeredAnimations(".nav-list", ".nav-link", "animate-fade-in");

    // Terms sections
    addStaggeredAnimations(
      ".terms-content",
      ".terms-section",
      "animate-on-scroll"
    );

    // Design sections
    addStaggeredAnimations(
      ".design-content",
      ".design-section",
      "animate-slide-left"
    );
  }, 500);

  // Sponsors Carousel Functionality
  const sponsorsTrack = document.getElementById("sponsorsTrack");
  const prevBtn = document.getElementById("prevBtn");
  const nextBtn = document.getElementById("nextBtn");
  const indicators = document.querySelectorAll(".indicator");

  if (sponsorsTrack && prevBtn && nextBtn) {
    let currentSlide = 0;
    const totalSlides = 5; // Number of original slides
    const slideWidth = 200; // Width of each slide in pixels

    // Function to update carousel position
    function updateCarousel() {
      const translateX = -currentSlide * slideWidth;
      sponsorsTrack.style.transform = `translateX(${translateX}px)`;

      // Update indicators
      indicators.forEach((indicator, index) => {
        indicator.classList.toggle("active", index === currentSlide);
      });
    }

    // Next slide function
    function nextSlide() {
      currentSlide = (currentSlide + 1) % totalSlides;
      updateCarousel();
    }

    // Previous slide function
    function prevSlide() {
      currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
      updateCarousel();
    }

    // Event listeners for navigation buttons
    nextBtn.addEventListener("click", nextSlide);
    prevBtn.addEventListener("click", prevSlide);

    // Event listeners for indicators
    indicators.forEach((indicator, index) => {
      indicator.addEventListener("click", () => {
        currentSlide = index;
        updateCarousel();
      });
    });

    // Pause auto-scroll on hover and resume on mouse leave
    sponsorsTrack.addEventListener("mouseenter", () => {
      sponsorsTrack.style.animationPlayState = "paused";
    });

    sponsorsTrack.addEventListener("mouseleave", () => {
      sponsorsTrack.style.animationPlayState = "running";
    });

    // Manual control overrides auto-scroll
    let manualControl = false;

    function enableManualControl() {
      if (!manualControl) {
        manualControl = true;
        sponsorsTrack.style.animation = "none";
        updateCarousel();
      }
    }

    prevBtn.addEventListener("click", enableManualControl);
    nextBtn.addEventListener("click", enableManualControl);
    indicators.forEach((indicator) => {
      indicator.addEventListener("click", enableManualControl);
    });

    // Touch/swipe support for mobile
    let startX = 0;
    let isDragging = false;

    sponsorsTrack.addEventListener("touchstart", (e) => {
      startX = e.touches[0].clientX;
      isDragging = true;
      enableManualControl();
    });

    sponsorsTrack.addEventListener("touchmove", (e) => {
      if (!isDragging) return;
      e.preventDefault();
    });

    sponsorsTrack.addEventListener("touchend", (e) => {
      if (!isDragging) return;
      isDragging = false;

      const endX = e.changedTouches[0].clientX;
      const diffX = startX - endX;

      if (Math.abs(diffX) > 50) {
        // Minimum swipe distance
        if (diffX > 0) {
          nextSlide();
        } else {
          prevSlide();
        }
      }
    });
  }
});
